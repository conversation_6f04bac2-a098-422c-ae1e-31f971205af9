from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, ValidationError as PydanticValidationError
import os
from datetime import datetime
from app.agent import BookingAgent
from app.utils.twilio_client import TwilioClient
from app.utils.database import DatabaseManager
from langchain_core.messages import HumanMessage
import logging
from app.utils.validation import (
    WebhookInput, ValidationError, sanitize_error_message,
    validate_request_size, log_validation_failure
)
from app.utils.exceptions import (
    BaseBookingException, ValidationException, ErrorContext, ErrorSeverity,
    log_exception, create_error_response, sanitize_error_for_user
)
from app.utils.cache import performance_monitor, get_cache_stats
from app.utils.voice_config import validate_voice_processing_config, is_voice_processing_enabled

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_allowed_origins() -> list[str]:
    """Get allowed origins from environment variable with secure defaults."""
    # Default origins for development: Streamlit (8501), React dev (3000), and common dev port (8080)
    default_origins = 'http://localhost:3000,http://localhost:8501,http://localhost:8080'
    allowed_origins_env = os.getenv('ALLOWED_ORIGINS', default_origins)

    # Split by comma and strip whitespace, filter out empty strings
    origins = [origin.strip() for origin in allowed_origins_env.split(',') if origin.strip()]

    # Log the configured origins for debugging (but not in production)
    if os.getenv('ENVIRONMENT', 'development') == 'development':
        logger.info(f"CORS allowed origins: {origins}")

    return origins

app = FastAPI()

# Validate voice processing configuration during startup
try:
    validate_voice_processing_config()
    logger.info("Voice processing configuration validation completed successfully")
except Exception as e:
    logger.warning(f"Voice processing configuration validation failed: {e}")
    logger.info("Application will continue with voice processing disabled")

agent = BookingAgent()
twilio_client = TwilioClient()
db = DatabaseManager()

# Enable CORS with secure configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=get_allowed_origins(),  # Secure: environment-based origins
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],  # Specific methods
    allow_headers=["*"],  # Keep flexible for API compatibility
)

class WhatsAppMessage(BaseModel):
    Body: str
    From: str

def _handle_voice_message_placeholder(validated_input: WebhookInput, phone_number: str, processing_enabled: bool = False) -> str:
    """
    Placeholder function for voice message processing with feature flag support.

    This function provides appropriate responses for voice messages based on whether
    voice processing is enabled or disabled, until the full voice processing
    pipeline is implemented in Phase 3.

    Args:
        validated_input: Validated webhook input containing voice message data
        phone_number: User's phone number for context
        processing_enabled: Whether voice processing is enabled and configured

    Returns:
        User-friendly message appropriate for the current configuration
    """
    try:
        # Import here to avoid circular imports and for language detection
        from app.utils.language import detect_language, translate_response

        # Detect language from any text content or default to English
        language = 'en'
        if validated_input.Body and validated_input.Body.strip():
            language = detect_language(validated_input.Body)

        # Create appropriate message based on language and processing status
        if processing_enabled:
            # Voice processing is enabled - indicate we're working on it
            fallback_messages = {
                'en': (
                    "I received your voice message! 🎤 Voice processing is enabled but still under development. "
                    "For now, please send your appointment request as a text message and I'll be happy to help you. "
                    "Voice message support coming soon!"
                ),
                'ar': (
                    "تلقيت رسالتك الصوتية! 🎤 معالجة الرسائل الصوتية مُفعّلة ولكن لا تزال قيد التطوير. "
                    "في الوقت الحالي، يرجى إرسال طلب موعدك كرسالة نصية وسأكون سعيداً لمساعدتك. "
                    "دعم الرسائل الصوتية قادم قريباً!"
                )
            }
        else:
            # Voice processing is disabled - standard fallback
            fallback_messages = {
                'en': (
                    "I received your voice message! 🎤 Currently, please send your appointment request "
                    "as a text message and I'll be happy to help you."
                ),
                'ar': (
                    "تلقيت رسالتك الصوتية! 🎤 حالياً، يرجى إرسال طلب موعدك "
                    "كرسالة نصية وسأكون سعيداً لمساعدتك."
                )
            }

        base_message = fallback_messages.get(language, fallback_messages['en'])

        # Log voice message attempt for analytics
        status = "enabled" if processing_enabled else "disabled"
        logger.info(f"Voice message fallback response sent to {phone_number} "
                   f"(MediaType: {validated_input.MediaContentType0}, Processing: {status})")

        return base_message

    except Exception as e:
        # Fallback to simple English message if translation fails
        logger.error(f"Error in voice message placeholder handler: {e}")
        return (
            "I received your voice message, but I can only process text messages right now. "
            "Please send your message as text and I'll help you with your appointment."
        )

@app.post("/webhook")
async def webhook(request: Request):
    # Initialize variables to prevent UnboundLocalError in exception handlers
    raw_body = ""
    raw_from = ""
    num_media = 0
    raw_media_content_type0 = None

    try:
        # Validate request size to prevent DoS attacks
        content_length = request.headers.get("content-length")
        if content_length and not validate_request_size(int(content_length)):
            logger.warning(f"Request size too large: {content_length}")
            raise HTTPException(status_code=413, detail="Request too large")

        # Get form data with error handling
        try:
            form_data = await request.form()
            raw_body = form_data.get("Body", "")
            raw_from = form_data.get("From", "")

            # Extract media parameters for voice message support
            raw_num_media = form_data.get("NumMedia", "0")
            raw_media_url0 = form_data.get("MediaUrl0")
            raw_media_content_type0 = form_data.get("MediaContentType0")

            # Convert NumMedia to integer, default to 0 if invalid
            try:
                num_media = int(raw_num_media) if raw_num_media else 0
            except (ValueError, TypeError):
                num_media = 0
                logger.warning(f"Invalid NumMedia value: {raw_num_media}, defaulting to 0")

        except Exception as form_error:
            logger.error(f"Failed to parse form data: {form_error}")
            raise HTTPException(
                status_code=400,
                detail="Invalid request format. Unable to parse form data."
            )

        # Validate and sanitize input using Pydantic model with media support
        try:
            validated_input = WebhookInput(
                Body=raw_body,
                From=raw_from,
                NumMedia=num_media,
                MediaUrl0=raw_media_url0,
                MediaContentType0=raw_media_content_type0
            )
            message_body = validated_input.Body
            phone_number = validated_input.From
        except PydanticValidationError as e:
            # Log validation failure for security monitoring
            media_info = f"NumMedia: {num_media}, MediaType: {raw_media_content_type0}" if num_media > 0 else "text message"
            log_validation_failure(
                field="webhook_input",
                value=f"Body: {raw_body[:50]}..., From: {raw_from}, Media: {media_info}",
                error=str(e),
                user_id=raw_from
            )
            raise HTTPException(
                status_code=400,
                detail="Invalid input format. Please check your message and try again."
            )

        # Keep the original phone number format from Twilio
        # It should already be in the format whatsapp:+XXXXXXXXXX

        # Detect voice messages and route accordingly
        if validated_input.is_voice_message():
            # Voice message detected - log and handle based on feature flag
            logger.info(f"Voice message detected from {phone_number}: "
                       f"MediaType={validated_input.MediaContentType0}, "
                       f"MediaUrl={validated_input.MediaUrl0[:50] + '...' if validated_input.MediaUrl0 else 'None'}")

            # Check if voice processing is enabled and available
            if is_voice_processing_enabled():
                # Voice processing is enabled - route to voice processing pipeline
                # TODO: This will be implemented in Phase 3 (Agent Integration)
                logger.info(f"Voice processing enabled - preparing to process voice message from {phone_number}")
                response = _handle_voice_message_placeholder(validated_input, phone_number, processing_enabled=True)
            else:
                # Voice processing disabled or not configured - provide fallback response
                logger.info(f"Voice processing disabled - providing fallback response to {phone_number}")
                response = _handle_voice_message_placeholder(validated_input, phone_number, processing_enabled=False)

        else:
            # Text message - process through existing workflow (unchanged)
            logger.debug(f"Text message received from {phone_number}: {message_body[:50]}...")

            # Create message in LangChain format
            message = HumanMessage(
                content=message_body,
                additional_kwargs={
                    "type": "user",
                    "user_id": phone_number
                }
            )

            # Process message through agent (existing functionality)
            response = agent.process_message(message_body, phone_number)

        # Send response via Twilio using the original phone number format
        twilio_client.send_message(phone_number, response)

        return {"status": "success"}

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except (ValidationError, ValidationException) as e:
        # Handle custom validation errors with structured logging
        context = ErrorContext(
            user_id=raw_from,
            operation="webhook_validation",
            additional_data={
                "endpoint": "/webhook",
                "has_media": num_media > 0,
                "media_type": raw_media_content_type0,
                "num_media": num_media
            }
        )

        # Convert legacy ValidationError to ValidationException if needed
        if isinstance(e, ValidationError) and not isinstance(e, ValidationException):
            validation_exception = ValidationException(
                message=str(e),
                field=getattr(e, 'field', None),
                context=context
            )
        else:
            validation_exception = e
            validation_exception.context = context

        log_exception(validation_exception)
        error_response = create_error_response(validation_exception, language='en')
        raise HTTPException(status_code=400, detail=error_response["message"])

    except BaseBookingException as e:
        # Handle all custom booking exceptions with structured logging
        context = ErrorContext(
            user_id=raw_from,
            operation="webhook_processing",
            additional_data={
                "endpoint": "/webhook",
                "has_media": num_media > 0,
                "media_type": raw_media_content_type0,
                "num_media": num_media
            }
        )
        e.context = context

        log_exception(e)
        error_response = create_error_response(e, language='en')

        # Determine HTTP status code based on error severity
        status_code = 400 if e.severity == ErrorSeverity.LOW else 500
        raise HTTPException(status_code=status_code, detail=error_response["message"])

    except Exception as e:
        # Handle unexpected exceptions with enhanced logging and sanitization
        context = ErrorContext(
            user_id=raw_from,
            operation="webhook_processing",
            additional_data={
                "endpoint": "/webhook",
                "error_type": type(e).__name__,
                "has_media": num_media > 0,
                "media_type": raw_media_content_type0,
                "num_media": num_media
            }
        )

        log_exception(e, context=context)
        safe_message = sanitize_error_for_user(e)
        raise HTTPException(status_code=500, detail=safe_message)

@app.get("/health")
async def health_check():
    """Enhanced health check with database connectivity and voice processing status."""
    try:
        # Test database connectivity
        db.execute_query("SELECT 1")

        # Check voice processing configuration
        from app.utils.voice_config import get_voice_config
        voice_config = get_voice_config()

        return {
            "status": "healthy",
            "database": "connected",
            "cache": "active",
            "voice_processing": {
                "enabled": voice_config.voice_processing_enabled,
                "available": voice_config.is_voice_processing_available(),
                "language": voice_config.speech_to_text_language_code
            }
        }
    except Exception as e:
        return {
            "status": "degraded",
            "database": "error",
            "error": str(e)
        }

@app.get("/performance")
async def performance_metrics():
    """Get performance metrics and optimization statistics."""
    try:
        return {
            "status": "success",
            "metrics": performance_monitor.get_performance_summary(),
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        context = ErrorContext(
            operation="performance_metrics",
            additional_data={"endpoint": "/performance"}
        )
        log_exception(e, context=context)
        return {
            "status": "error",
            "message": "Failed to retrieve performance metrics",
            "timestamp": datetime.utcnow().isoformat()
        }